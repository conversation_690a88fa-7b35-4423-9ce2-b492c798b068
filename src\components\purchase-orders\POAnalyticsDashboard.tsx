"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { POStatusBadge } from "./POStatusBadge";
import { 
  TrendingUp, 
  TrendingDown, 
  Clock, 
  AlertTriangle, 
  CheckCircle, 
  DollarSign,
  Package,
  Users,
  Calendar
} from "lucide-react";
import { POStatus, POPriority } from "@prisma/client";

interface POAnalyticsData {
  summary: {
    totalPOs: number;
    totalValue: number;
    overdueCount: number;
    onTimeDeliveryRate: number;
  };
  statusBreakdown: Array<{
    status: POStatus;
    count: number;
    totalValue: number;
  }>;
  priorityBreakdown: Array<{
    priority: POPriority;
    count: number;
    totalValue: number;
  }>;
  overduePOs: Array<{
    id: string;
    supplier: string;
    total: number;
    expectedDeliveryDate: string;
    daysOverdue: number;
    status: POStatus;
    priority: POPriority;
  }>;
  recentStatusChanges: Array<{
    id: string;
    purchaseOrderId: string;
    supplier: string;
    fromStatus: POStatus | null;
    toStatus: POStatus;
    reason: string;
    notes?: string;
    changedBy: string;
    changedAt: string;
  }>;
  supplierPerformance: Array<{
    supplierId: string;
    supplierName: string;
    completedOrders: number;
    averageOrderValue: number;
  }>;
}

interface POAnalyticsDashboardProps {
  className?: string;
}

export function POAnalyticsDashboard({ className = "" }: POAnalyticsDashboardProps) {
  const [data, setData] = useState<POAnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchAnalytics();
  }, []);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/purchase-orders/analytics');
      if (!response.ok) throw new Error('Failed to fetch analytics');
      const analyticsData = await response.json();
      setData(analyticsData);
    } catch (error) {
      console.error('Error fetching analytics:', error);
      setError('Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className={`text-center p-8 ${className}`}>
        <AlertTriangle className="h-12 w-12 mx-auto text-red-500 mb-4" />
        <p className="text-red-600">{error || 'Failed to load analytics'}</p>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Purchase Orders</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.summary.totalPOs}</div>
            <p className="text-xs text-muted-foreground">
              Active purchase orders
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Value</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              Rp {Number(data.summary.totalValue).toLocaleString("id-ID")}
            </div>
            <p className="text-xs text-muted-foreground">
              Total order value
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Overdue Orders</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{data.summary.overdueCount}</div>
            <p className="text-xs text-muted-foreground">
              Require immediate attention
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">On-Time Delivery</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {data.summary.onTimeDeliveryRate}%
            </div>
            <p className="text-xs text-muted-foreground">
              Delivery performance
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="status" className="space-y-4">
        <TabsList>
          <TabsTrigger value="status">Status Breakdown</TabsTrigger>
          <TabsTrigger value="priority">Priority Analysis</TabsTrigger>
          <TabsTrigger value="overdue">Overdue Orders</TabsTrigger>
          <TabsTrigger value="activity">Recent Activity</TabsTrigger>
        </TabsList>

        <TabsContent value="status" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Purchase Order Status Distribution</CardTitle>
              <CardDescription>
                Current status breakdown of all purchase orders
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data.statusBreakdown.map((item) => (
                  <div key={item.status} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <POStatusBadge status={item.status} size="sm" />
                      <span className="text-sm font-medium">{item.count} orders</span>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium">
                        Rp {Number(item.totalValue).toLocaleString("id-ID")}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {((item.count / data.summary.totalPOs) * 100).toFixed(1)}%
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="priority" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Priority Distribution</CardTitle>
              <CardDescription>
                Purchase orders by priority level
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data.priorityBreakdown.map((item) => (
                  <div key={item.priority} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <POStatusBadge status="DRAFT" priority={item.priority} showIcon={false} size="sm" />
                      <span className="text-sm font-medium">{item.count} orders</span>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium">
                        Rp {Number(item.totalValue).toLocaleString("id-ID")}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {((item.count / data.summary.totalPOs) * 100).toFixed(1)}%
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="overdue" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Overdue Purchase Orders</CardTitle>
              <CardDescription>
                Orders that have passed their expected delivery date
              </CardDescription>
            </CardHeader>
            <CardContent>
              {data.overduePOs.length === 0 ? (
                <div className="text-center py-8">
                  <CheckCircle className="h-12 w-12 mx-auto text-green-500 mb-4" />
                  <p className="text-green-600 font-medium">No overdue orders!</p>
                  <p className="text-sm text-muted-foreground">All orders are on track</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {data.overduePOs.map((po) => (
                    <div key={po.id} className="flex items-center justify-between p-3 border rounded-lg bg-red-50">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{po.id.slice(-8).toUpperCase()}</span>
                          <POStatusBadge status={po.status} priority={po.priority} size="sm" />
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {po.supplier} • Rp {Number(po.total).toLocaleString("id-ID")}
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge variant="destructive" className="text-xs">
                          {po.daysOverdue} days overdue
                        </Badge>
                        <div className="text-xs text-muted-foreground mt-1">
                          Expected: {new Date(po.expectedDeliveryDate).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="activity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Status Changes</CardTitle>
              <CardDescription>
                Latest purchase order status updates
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data.recentStatusChanges.map((change) => (
                  <div key={change.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{change.purchaseOrderId.slice(-8).toUpperCase()}</span>
                        <span className="text-sm text-muted-foreground">•</span>
                        <span className="text-sm">{change.supplier}</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        {change.fromStatus && <POStatusBadge status={change.fromStatus} size="sm" />}
                        {change.fromStatus && <span>→</span>}
                        <POStatusBadge status={change.toStatus} size="sm" />
                      </div>
                      {change.notes && (
                        <p className="text-xs text-muted-foreground">{change.notes}</p>
                      )}
                    </div>
                    <div className="text-right text-xs text-muted-foreground">
                      <div>{change.changedBy}</div>
                      <div>{new Date(change.changedAt).toLocaleDateString()}</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
