"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import {
  Search,
  Filter,
  Calendar as CalendarIcon,
  Package,
  Truck,
  AlertTriangle,
  CheckCircle,
  Clock,
  Eye,
  Download,
  Loader2,
  FileText,
} from "lucide-react";
import { format } from "date-fns";
import { toast } from "sonner";
import Link from "next/link";

interface ReceiptHistoryItem {
  id: string;
  receivedAt: string;
  notes?: string;
  discrepancyReason?: string;
  purchaseOrder: {
    id: string;
    orderDate: string;
    status: string;
    total: number;
    supplier: {
      id: string;
      name: string;
      contactPerson?: string;
    };
    items: Array<{
      id: string;
      quantity: number;
      receivedQuantity: number;
      product: {
        id: string;
        name: string;
        sku: string;
      };
    }>;
  };
  receivedBy: {
    id: string;
    name: string;
    email: string;
  };
  items: Array<{
    id: string;
    receivedQuantity: number;
    discrepancyQuantity: number;
    discrepancyReason?: string;
    notes?: string;
  }>;
  metrics: {
    totalItemsReceived: number;
    totalItemsOrdered: number;
    totalDiscrepancies: number;
    hasDiscrepancies: boolean;
    batchesCreated: number;
    fulfillmentPercentage: number;
  };
}

interface PaginationInfo {
  total: number;
  page: number;
  limit: number;
  pages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

interface Supplier {
  id: string;
  name: string;
}

export default function ReceiptHistoryPage() {
  const [receipts, setReceipts] = useState<ReceiptHistoryItem[]>([]);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<PaginationInfo>({
    total: 0,
    page: 1,
    limit: 20,
    pages: 0,
    hasNext: false,
    hasPrev: false,
  });

  // Filter states
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedSupplier, setSelectedSupplier] = useState<string>("all");
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [startDate, setStartDate] = useState<Date | undefined>();
  const [endDate, setEndDate] = useState<Date | undefined>();
  const [sortBy, setSortBy] = useState("receivedAt");
  const [sortOrder, setSortOrder] = useState("desc");

  // Fetch suppliers for filter dropdown
  const fetchSuppliers = async () => {
    try {
      const response = await fetch("/api/suppliers");
      if (!response.ok) throw new Error("Failed to fetch suppliers");
      const data = await response.json();
      setSuppliers(data.suppliers || []);
    } catch (error) {
      console.error("Error fetching suppliers:", error);
    }
  };

  // Fetch receipt history with filters
  const fetchReceiptHistory = async (page = 1) => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: page.toString(),
        limit: pagination.limit.toString(),
        sortBy,
        sortOrder,
      });

      if (searchTerm) params.append("search", searchTerm);
      if (selectedSupplier && selectedSupplier !== "all")
        params.append("supplierId", selectedSupplier);
      if (selectedStatus && selectedStatus !== "all") params.append("status", selectedStatus);
      if (startDate) params.append("startDate", startDate.toISOString());
      if (endDate) params.append("endDate", endDate.toISOString());

      const response = await fetch(`/api/inventory/receipt-history?${params}`);
      if (!response.ok) {
        throw new Error("Failed to fetch receipt history");
      }

      const data = await response.json();
      setReceipts(data.receipts);
      setPagination(data.pagination);
    } catch (error) {
      console.error("Error fetching receipt history:", error);
      setError("Failed to load receipt history");
      toast.error("Failed to load receipt history");
    } finally {
      setLoading(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchSuppliers();
    fetchReceiptHistory();
  }, []);

  // Refetch when filters change
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchReceiptHistory(1);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchTerm, selectedSupplier, selectedStatus, startDate, endDate, sortBy, sortOrder]);

  const handlePageChange = (newPage: number) => {
    fetchReceiptHistory(newPage);
  };

  const clearFilters = () => {
    setSearchTerm("");
    setSelectedSupplier("all");
    setSelectedStatus("all");
    setStartDate(undefined);
    setEndDate(undefined);
    setSortBy("receivedAt");
    setSortOrder("desc");
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "dd/MM/yyyy HH:mm");
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "RECEIVED":
        return "default";
      case "PARTIALLY_RECEIVED":
        return "secondary";
      default:
        return "outline";
    }
  };

  const getDiscrepancyBadge = (hasDiscrepancies: boolean) => {
    if (hasDiscrepancies) {
      return (
        <Badge variant="destructive" className="flex items-center gap-1">
          <AlertTriangle className="h-3 w-3" />
          Discrepancies
        </Badge>
      );
    }
    return (
      <Badge variant="default" className="flex items-center gap-1">
        <CheckCircle className="h-3 w-3" />
        Complete
      </Badge>
    );
  };

  return (
    <MainLayout>
      <PageHeader
        title="Receipt History & Audit Trail"
        description="Comprehensive tracking of all goods receipt activities with detailed logs"
        actions={
          <div className="flex gap-2">
            <Button variant="outline" onClick={clearFilters}>
              Clear Filters
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        }
      />

      <div className="space-y-6">
        {/* Filters Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filters & Search
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Search */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Search</label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="PO number, supplier, product..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              {/* Supplier Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Supplier</label>
                <Select value={selectedSupplier} onValueChange={setSelectedSupplier}>
                  <SelectTrigger>
                    <SelectValue placeholder="All suppliers" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All suppliers</SelectItem>
                    {suppliers.map((supplier) => (
                      <SelectItem key={supplier.id} value={supplier.id}>
                        {supplier.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Status Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Status</label>
                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                  <SelectTrigger>
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All statuses</SelectItem>
                    <SelectItem value="RECEIVED">Fully Received</SelectItem>
                    <SelectItem value="PARTIALLY_RECEIVED">Partially Received</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Date Range */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Date Range</label>
                <div className="flex gap-2">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" size="sm" className="flex-1">
                        <CalendarIcon className="h-4 w-4 mr-2" />
                        {startDate ? format(startDate, "dd/MM") : "From"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={startDate}
                        onSelect={setStartDate}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" size="sm" className="flex-1">
                        <CalendarIcon className="h-4 w-4 mr-2" />
                        {endDate ? format(endDate, "dd/MM") : "To"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={endDate}
                        onSelect={setEndDate}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Receipt History Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Receipt History
            </CardTitle>
            <CardDescription>{pagination.total} total receipts found</CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex justify-center items-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : error ? (
              <div className="text-center py-8">
                <p className="text-red-600">{error}</p>
                <Button onClick={() => fetchReceiptHistory()} className="mt-4">
                  Try Again
                </Button>
              </div>
            ) : receipts.length > 0 ? (
              <>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>PO Number</TableHead>
                      <TableHead>Received Date</TableHead>
                      <TableHead>Supplier</TableHead>
                      <TableHead>Received By</TableHead>
                      <TableHead>Items</TableHead>
                      <TableHead>Total Value</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {receipts.map((receipt) => (
                      <TableRow key={receipt.id}>
                        <TableCell className="font-medium">
                          #{receipt.purchaseOrder.id.slice(-8).toUpperCase()}
                        </TableCell>
                        <TableCell>{formatDate(receipt.receivedAt)}</TableCell>
                        <TableCell>{receipt.purchaseOrder.supplier.name}</TableCell>
                        <TableCell>{receipt.receivedBy.name}</TableCell>
                        <TableCell>
                          <div className="flex flex-col">
                            <span>
                              {receipt.metrics.totalItemsReceived} /{" "}
                              {receipt.metrics.totalItemsOrdered}
                            </span>
                            <span className="text-sm text-muted-foreground">
                              {receipt.metrics.fulfillmentPercentage}% fulfilled
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>{formatCurrency(receipt.purchaseOrder.total)}</TableCell>
                        <TableCell>
                          <div className="flex flex-col gap-1">
                            <Badge variant={getStatusBadgeVariant(receipt.purchaseOrder.status)}>
                              {receipt.purchaseOrder.status.replace("_", " ")}
                            </Badge>
                            {getDiscrepancyBadge(receipt.metrics.hasDiscrepancies)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-2">
                            <Button variant="outline" size="sm" asChild>
                              <Link href={`/inventory/receipt-history/${receipt.id}`}>
                                <Eye className="h-4 w-4" />
                              </Link>
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                {/* Pagination */}
                {pagination.pages > 1 && (
                  <div className="flex justify-between items-center mt-4">
                    <p className="text-sm text-muted-foreground">
                      Showing {(pagination.page - 1) * pagination.limit + 1} to{" "}
                      {Math.min(pagination.page * pagination.limit, pagination.total)} of{" "}
                      {pagination.total} results
                    </p>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(pagination.page - 1)}
                        disabled={!pagination.hasPrev}
                      >
                        Previous
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(pagination.page + 1)}
                        disabled={!pagination.hasNext}
                      >
                        Next
                      </Button>
                    </div>
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-muted-foreground">No receipt history found</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}
