import { NextRequest, NextResponse } from 'next/server';
import { verifyAuthToken } from '@/auth';
import { prisma } from '@/auth';

// GET /api/purchase-orders/[id]/status-history - Get PO status history
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { id } = params;

    // Verify PO exists
    const purchaseOrder = await prisma.purchaseOrder.findUnique({
      where: { id },
      select: { id: true, status: true },
    });

    if (!purchaseOrder) {
      return NextResponse.json({ error: 'Purchase order not found' }, { status: 404 });
    }

    // Get status history
    const statusHistory = await prisma.pOStatusHistory.findMany({
      where: { purchaseOrderId: id },
      include: {
        changedBy: {
          select: { id: true, name: true, email: true },
        },
      },
      orderBy: { changedAt: 'desc' },
    });

    return NextResponse.json({
      purchaseOrderId: id,
      currentStatus: purchaseOrder.status,
      statusHistory,
    });
  } catch (error) {
    console.error('Error fetching PO status history:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
