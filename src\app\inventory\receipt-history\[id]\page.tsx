"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  ArrowLeft, 
  Package, 
  Truck, 
  User, 
  Calendar,
  AlertTriangle,
  CheckCircle,
  Clock,
  FileText,
  Activity,
  Database,
  Loader2
} from "lucide-react";
import { format } from "date-fns";
import { toast } from "sonner";
import Link from "next/link";

interface DetailedReceipt {
  id: string;
  receivedAt: string;
  notes?: string;
  discrepancyReason?: string;
  createdAt: string;
  purchaseOrder: {
    id: string;
    orderDate: string;
    status: string;
    total: number;
    subtotal: number;
    tax: number;
    notes?: string;
    createdAt: string;
    approvedAt?: string;
    supplier: {
      id: string;
      name: string;
      contactPerson?: string;
      phone?: string;
      email?: string;
      address?: string;
    };
    createdBy: {
      id: string;
      name: string;
      email: string;
    };
    approvedBy?: {
      id: string;
      name: string;
      email: string;
    };
    items: Array<{
      id: string;
      quantity: number;
      receivedQuantity: number;
      unitPrice: number;
      subtotal: number;
      product: {
        id: string;
        name: string;
        sku: string;
        category: {
          id: string;
          name: string;
        };
        unit: {
          id: string;
          name: string;
          abbreviation: string;
        };
      };
    }>;
  };
  receivedBy: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
  items: Array<{
    id: string;
    receivedQuantity: number;
    discrepancyQuantity: number;
    discrepancyReason?: string;
    notes?: string;
    purchaseOrderItem: {
      id: string;
      quantity: number;
      unitPrice: number;
      product: {
        id: string;
        name: string;
        sku: string;
        unit: {
          name: string;
          abbreviation: string;
        };
      };
    };
  }>;
  metrics: {
    totalItemsOrdered: number;
    totalItemsReceived: number;
    totalDiscrepancies: number;
    totalValue: number;
    batchesCreated: number;
    uniqueProducts: number;
    fulfillmentPercentage: number;
    hasDiscrepancies: boolean;
    processingTime: number;
  };
  relatedBatches: Array<{
    id: string;
    batchNumber?: string;
    quantity: number;
    remainingQuantity: number;
    expiryDate?: string;
    status: string;
    product: {
      id: string;
      name: string;
      sku: string;
    };
  }>;
  relatedStockHistory: Array<{
    id: string;
    date: string;
    previousQuantity: number;
    newQuantity: number;
    changeQuantity: number;
    source: string;
    notes?: string;
    product: {
      id: string;
      name: string;
      sku: string;
    };
    user: {
      id: string;
      name: string;
    };
  }>;
  relatedActivityLogs: Array<{
    id: string;
    action: string;
    details?: string;
    timestamp: string;
    user: {
      id: string;
      name: string;
    };
  }>;
  auditTrail: {
    created: {
      at: string;
      by: {
        id: string;
        name: string;
        email: string;
      };
    };
    purchaseOrder: {
      created: {
        at: string;
        by: {
          id: string;
          name: string;
          email: string;
        };
      };
      approved?: {
        at: string;
        by: {
          id: string;
          name: string;
          email: string;
        };
      };
    };
  };
}

export default function ReceiptDetailPage() {
  const params = useParams();
  const receiptId = params.id as string;
  
  const [receipt, setReceipt] = useState<DetailedReceipt | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchReceiptDetails = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/inventory/receipt-history/${receiptId}`);
      if (!response.ok) {
        throw new Error("Failed to fetch receipt details");
      }

      const data = await response.json();
      setReceipt(data);
    } catch (error) {
      console.error("Error fetching receipt details:", error);
      setError("Failed to load receipt details");
      toast.error("Failed to load receipt details");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (receiptId) {
      fetchReceiptDetails();
    }
  }, [receiptId]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "dd/MM/yyyy HH:mm:ss");
  };

  const formatDateShort = (dateString: string) => {
    return format(new Date(dateString), "dd/MM/yyyy");
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "RECEIVED":
        return "default";
      case "PARTIALLY_RECEIVED":
        return "secondary";
      case "ACTIVE":
        return "default";
      case "EXPIRED":
        return "destructive";
      default:
        return "outline";
    }
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="flex justify-center items-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </MainLayout>
    );
  }

  if (error || !receipt) {
    return (
      <MainLayout>
        <div className="text-center py-8">
          <p className="text-red-600">{error || "Receipt not found"}</p>
          <Button asChild className="mt-4">
            <Link href="/inventory/receipt-history">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Receipt History
            </Link>
          </Button>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <PageHeader
        title={`Receipt Details - PO #${receipt.purchaseOrder.id.slice(-8).toUpperCase()}`}
        description={`Received on ${formatDate(receipt.receivedAt)} by ${receipt.receivedBy.name}`}
        actions={
          <Button variant="outline" asChild>
            <Link href="/inventory/receipt-history">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to History
            </Link>
          </Button>
        }
      />

      <div className="space-y-6">
        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Items Received</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {receipt.metrics.totalItemsReceived} / {receipt.metrics.totalItemsOrdered}
              </div>
              <p className="text-xs text-muted-foreground">
                {receipt.metrics.fulfillmentPercentage}% fulfillment rate
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Value</CardTitle>
              <Truck className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(receipt.metrics.totalValue)}</div>
              <p className="text-xs text-muted-foreground">
                Received goods value
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Batches Created</CardTitle>
              <Database className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{receipt.metrics.batchesCreated}</div>
              <p className="text-xs text-muted-foreground">
                Stock batches generated
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Status</CardTitle>
              {receipt.metrics.hasDiscrepancies ? (
                <AlertTriangle className="h-4 w-4 text-destructive" />
              ) : (
                <CheckCircle className="h-4 w-4 text-green-600" />
              )}
            </CardHeader>
            <CardContent>
              <div className="flex flex-col gap-1">
                <Badge variant={getStatusBadgeVariant(receipt.purchaseOrder.status)}>
                  {receipt.purchaseOrder.status.replace("_", " ")}
                </Badge>
                {receipt.metrics.hasDiscrepancies ? (
                  <Badge variant="destructive" className="text-xs">
                    Has Discrepancies
                  </Badge>
                ) : (
                  <Badge variant="default" className="text-xs">
                    Complete
                  </Badge>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Information Tabs */}
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="items">Received Items</TabsTrigger>
            <TabsTrigger value="batches">Stock Batches</TabsTrigger>
            <TabsTrigger value="audit">Audit Trail</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Purchase Order Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Purchase Order Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">PO Number</label>
                      <p className="font-medium">#{receipt.purchaseOrder.id.slice(-8).toUpperCase()}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Order Date</label>
                      <p>{formatDateShort(receipt.purchaseOrder.orderDate)}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Supplier</label>
                      <p className="font-medium">{receipt.purchaseOrder.supplier.name}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Total Amount</label>
                      <p className="font-medium">{formatCurrency(receipt.purchaseOrder.total)}</p>
                    </div>
                  </div>
                  {receipt.purchaseOrder.notes && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">PO Notes</label>
                      <p className="text-sm">{receipt.purchaseOrder.notes}</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Receipt Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Package className="h-5 w-5" />
                    Receipt Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Received Date</label>
                      <p className="font-medium">{formatDate(receipt.receivedAt)}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Received By</label>
                      <p className="font-medium">{receipt.receivedBy.name}</p>
                      <p className="text-sm text-muted-foreground">{receipt.receivedBy.role}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Unique Products</label>
                      <p>{receipt.metrics.uniqueProducts}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Total Discrepancies</label>
                      <p className={receipt.metrics.totalDiscrepancies > 0 ? "text-destructive font-medium" : ""}>
                        {receipt.metrics.totalDiscrepancies}
                      </p>
                    </div>
                  </div>
                  {receipt.notes && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Receipt Notes</label>
                      <p className="text-sm">{receipt.notes}</p>
                    </div>
                  )}
                  {receipt.discrepancyReason && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Discrepancy Reason</label>
                      <p className="text-sm text-destructive">{receipt.discrepancyReason}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="items" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Received Items Details</CardTitle>
                <CardDescription>
                  Detailed breakdown of all items received in this transaction
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Product</TableHead>
                      <TableHead>SKU</TableHead>
                      <TableHead>Ordered</TableHead>
                      <TableHead>Received</TableHead>
                      <TableHead>Unit Price</TableHead>
                      <TableHead>Discrepancy</TableHead>
                      <TableHead>Notes</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {receipt.items.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell className="font-medium">
                          {item.purchaseOrderItem.product.name}
                        </TableCell>
                        <TableCell>{item.purchaseOrderItem.product.sku}</TableCell>
                        <TableCell>
                          {item.purchaseOrderItem.quantity} {item.purchaseOrderItem.product.unit.abbreviation}
                        </TableCell>
                        <TableCell>
                          {item.receivedQuantity} {item.purchaseOrderItem.product.unit.abbreviation}
                        </TableCell>
                        <TableCell>{formatCurrency(item.purchaseOrderItem.unitPrice)}</TableCell>
                        <TableCell>
                          {item.discrepancyQuantity !== 0 ? (
                            <div className="flex flex-col">
                              <span className={item.discrepancyQuantity > 0 ? "text-green-600" : "text-destructive"}>
                                {item.discrepancyQuantity > 0 ? "+" : ""}{item.discrepancyQuantity}
                              </span>
                              {item.discrepancyReason && (
                                <span className="text-xs text-muted-foreground">{item.discrepancyReason}</span>
                              )}
                            </div>
                          ) : (
                            <span className="text-muted-foreground">-</span>
                          )}
                        </TableCell>
                        <TableCell>
                          {item.notes ? (
                            <span className="text-sm">{item.notes}</span>
                          ) : (
                            <span className="text-muted-foreground">-</span>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="batches" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Stock Batches Created</CardTitle>
                <CardDescription>
                  Stock batches generated during this receipt process
                </CardDescription>
              </CardHeader>
              <CardContent>
                {receipt.relatedBatches.length > 0 ? (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Product</TableHead>
                        <TableHead>Batch Number</TableHead>
                        <TableHead>Quantity</TableHead>
                        <TableHead>Remaining</TableHead>
                        <TableHead>Expiry Date</TableHead>
                        <TableHead>Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {receipt.relatedBatches.map((batch) => (
                        <TableRow key={batch.id}>
                          <TableCell className="font-medium">{batch.product.name}</TableCell>
                          <TableCell>{batch.batchNumber || "Auto-generated"}</TableCell>
                          <TableCell>{batch.quantity}</TableCell>
                          <TableCell>{batch.remainingQuantity}</TableCell>
                          <TableCell>
                            {batch.expiryDate ? formatDateShort(batch.expiryDate) : "No expiry"}
                          </TableCell>
                          <TableCell>
                            <Badge variant={getStatusBadgeVariant(batch.status)}>
                              {batch.status}
                            </Badge>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                ) : (
                  <div className="text-center py-8">
                    <Database className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                    <p className="text-muted-foreground">No stock batches were created for this receipt</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="audit" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Activity Logs */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="h-5 w-5" />
                    Activity Logs
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {receipt.relatedActivityLogs.length > 0 ? (
                    <div className="space-y-3">
                      {receipt.relatedActivityLogs.map((log) => (
                        <div key={log.id} className="flex items-start gap-3 p-3 border rounded-lg">
                          <Clock className="h-4 w-4 mt-1 text-muted-foreground" />
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <span className="font-medium">{log.action.replace("_", " ")}</span>
                              <span className="text-xs text-muted-foreground">
                                {formatDate(log.timestamp)}
                              </span>
                            </div>
                            {log.details && (
                              <p className="text-sm text-muted-foreground mt-1">{log.details}</p>
                            )}
                            <p className="text-xs text-muted-foreground">by {log.user.name}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-4">
                      <p className="text-muted-foreground">No activity logs found</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Stock History */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Database className="h-5 w-5" />
                    Stock History
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {receipt.relatedStockHistory.length > 0 ? (
                    <div className="space-y-3">
                      {receipt.relatedStockHistory.map((history) => (
                        <div key={history.id} className="flex items-start gap-3 p-3 border rounded-lg">
                          <Package className="h-4 w-4 mt-1 text-muted-foreground" />
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <span className="font-medium">{history.product.name}</span>
                              <span className="text-xs text-muted-foreground">
                                {formatDate(history.date)}
                              </span>
                            </div>
                            <p className="text-sm">
                              {history.previousQuantity} → {history.newQuantity} 
                              <span className="text-green-600 ml-1">
                                (+{history.changeQuantity})
                              </span>
                            </p>
                            {history.notes && (
                              <p className="text-xs text-muted-foreground mt-1">{history.notes}</p>
                            )}
                            <p className="text-xs text-muted-foreground">by {history.user.name}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-4">
                      <p className="text-muted-foreground">No stock history found</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
}
