"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { POStatusBadge } from "./POStatusBadge";
import { Loader2, History, ArrowRight, User, Calendar, MessageSquare } from "lucide-react";
import { POStatus, POStatusChangeReason } from "@prisma/client";
import { PO_STATUS_CHANGE_REASONS } from "@/lib/po-status-management";

interface StatusHistoryEntry {
  id: string;
  fromStatus: POStatus | null;
  toStatus: POStatus;
  reason: POStatusChangeReason;
  notes?: string;
  changedAt: string;
  changedBy: {
    id: string;
    name: string;
    email: string;
  };
}

interface POStatusHistoryData {
  purchaseOrderId: string;
  currentStatus: POStatus;
  statusHistory: StatusHistoryEntry[];
}

interface POStatusHistoryProps {
  purchaseOrderId: string;
  className?: string;
}

export function POStatusHistory({ purchaseOrderId, className = "" }: POStatusHistoryProps) {
  const [data, setData] = useState<POStatusHistoryData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchStatusHistory();
  }, [purchaseOrderId]);

  const fetchStatusHistory = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/purchase-orders/${purchaseOrderId}/status-history`);
      if (!response.ok) throw new Error('Failed to fetch status history');
      const historyData = await response.json();
      setData(historyData);
    } catch (error) {
      console.error('Error fetching status history:', error);
      setError('Failed to load status history');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Status History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-8">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="ml-2">Loading status history...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !data) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Status History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center p-8">
            <p className="text-red-600">{error || 'Failed to load status history'}</p>
            <Button variant="outline" onClick={fetchStatusHistory} className="mt-4">
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <History className="h-5 w-5" />
          Status History
        </CardTitle>
        <CardDescription>
          Complete timeline of status changes for this purchase order
        </CardDescription>
      </CardHeader>
      <CardContent>
        {data.statusHistory.length === 0 ? (
          <div className="text-center p-8">
            <History className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <p className="text-muted-foreground">No status changes recorded yet</p>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Current Status */}
            <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg border-l-4 border-blue-500">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <POStatusBadge status={data.currentStatus} size="sm" />
                  <Badge variant="outline" className="text-xs">Current</Badge>
                </div>
                <p className="text-sm text-muted-foreground">
                  Current status of the purchase order
                </p>
              </div>
            </div>

            {/* Status History Timeline */}
            <div className="relative">
              {data.statusHistory.map((entry, index) => (
                <div key={entry.id} className="relative">
                  {/* Timeline line */}
                  {index < data.statusHistory.length - 1 && (
                    <div className="absolute left-6 top-12 bottom-0 w-0.5 bg-gray-200" />
                  )}
                  
                  <div className="flex gap-4 pb-6">
                    {/* Timeline dot */}
                    <div className="flex-shrink-0 w-12 h-12 bg-white border-2 border-gray-200 rounded-full flex items-center justify-center">
                      <ArrowRight className="h-4 w-4 text-gray-500" />
                    </div>
                    
                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="bg-white border rounded-lg p-4 shadow-sm">
                        <div className="flex items-start justify-between mb-3">
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              {entry.fromStatus && (
                                <>
                                  <POStatusBadge status={entry.fromStatus} size="sm" />
                                  <ArrowRight className="h-3 w-3 text-muted-foreground" />
                                </>
                              )}
                              <POStatusBadge status={entry.toStatus} size="sm" />
                            </div>
                            <div className="text-sm font-medium">
                              {PO_STATUS_CHANGE_REASONS[entry.reason]}
                            </div>
                          </div>
                          <div className="text-right text-xs text-muted-foreground">
                            <div className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              {new Date(entry.changedAt).toLocaleDateString()}
                            </div>
                            <div className="mt-1">
                              {new Date(entry.changedAt).toLocaleTimeString()}
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                          <User className="h-3 w-3" />
                          <span>{entry.changedBy.name}</span>
                          <span>•</span>
                          <span>{entry.changedBy.email}</span>
                        </div>
                        
                        {entry.notes && (
                          <div className="mt-3 p-2 bg-gray-50 rounded text-sm">
                            <div className="flex items-start gap-2">
                              <MessageSquare className="h-3 w-3 mt-0.5 text-muted-foreground flex-shrink-0" />
                              <span className="text-muted-foreground">{entry.notes}</span>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

interface POStatusHistoryModalProps {
  purchaseOrderId: string;
  trigger?: React.ReactNode;
}

export function POStatusHistoryModal({ purchaseOrderId, trigger }: POStatusHistoryModalProps) {
  const [open, setOpen] = useState(false);

  return (
    <>
      <div onClick={() => setOpen(true)} className="cursor-pointer">
        {trigger || (
          <Button variant="outline" size="sm">
            <History className="h-4 w-4 mr-2" />
            View History
          </Button>
        )}
      </div>
      
      {open && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[80vh] overflow-hidden">
            <div className="p-4 border-b flex items-center justify-between">
              <h2 className="text-lg font-semibold">Purchase Order Status History</h2>
              <Button variant="ghost" size="sm" onClick={() => setOpen(false)}>
                ×
              </Button>
            </div>
            <div className="overflow-y-auto max-h-[calc(80vh-80px)]">
              <POStatusHistory purchaseOrderId={purchaseOrderId} className="border-0 shadow-none" />
            </div>
          </div>
        </div>
      )}
    </>
  );
}
