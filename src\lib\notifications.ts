import { prisma } from '@/lib/prisma';

export interface CreateNotificationData {
  userId: string;
  title: string;
  message: string;
  type: 'SYSTEM' | 'MESSAGE' | 'ALERT' | 'INFO' | 'PURCHASE_ORDER_APPROVAL' | 'PURCHASE_ORDER_APPROVED' | 'PURCHASE_ORDER_REJECTED' | 'PURCHASE_ORDER_RECEIVED' | 'PO_OVERDUE' | 'PO_ON_HOLD' | 'PO_EXPEDITED' | 'PO_CANCELLED' | 'PO_ORDERED';
  purchaseOrderId?: string;
  actionUrl?: string;
  metadata?: any;
}

/**
 * Create a notification for a user
 */
export async function createNotification(data: CreateNotificationData) {
  try {
    // Validate purchaseOrderId exists if provided
    if (data.purchaseOrderId) {
      const poExists = await prisma.purchaseOrder.findUnique({
        where: { id: data.purchaseOrderId },
        select: { id: true },
      });

      if (!poExists) {
        console.warn(`Purchase Order ${data.purchaseOrderId} not found, creating notification without purchaseOrderId`);
        // Create notification without purchaseOrderId to avoid foreign key constraint
        const { purchaseOrderId, ...notificationData } = data;
        const notification = await prisma.notification.create({
          data: notificationData,
        });
        console.log(`Notification created: ${notification.id} for user ${data.userId} (without PO reference)`);
        return notification;
      }
    }

    const notification = await prisma.notification.create({
      data: {
        userId: data.userId,
        title: data.title,
        message: data.message,
        type: data.type,
        purchaseOrderId: data.purchaseOrderId,
        actionUrl: data.actionUrl,
        metadata: data.metadata,
      },
    });

    console.log(`Notification created: ${notification.id} for user ${data.userId}`);
    return notification;
  } catch (error) {
    console.error('Error creating notification:', error);
    throw error;
  }
}

/**
 * Create notifications for multiple users
 */
export async function createBulkNotifications(notifications: CreateNotificationData[]) {
  try {
    const results = await Promise.all(
      notifications.map(notification => createNotification(notification))
    );
    console.log(`Created ${results.length} notifications`);
    return results;
  } catch (error) {
    console.error('Error creating bulk notifications:', error);
    throw error;
  }
}

/**
 * Get users with approval permissions for purchase orders
 */
export async function getApprovalUsers() {
  try {
    const users = await prisma.user.findMany({
      where: {
        role: {
          in: ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN']
        },
        active: true,
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
      },
    });

    return users;
  } catch (error) {
    console.error('Error fetching approval users:', error);
    throw error;
  }
}

/**
 * Create purchase order approval notifications
 */
export async function createPOApprovalNotifications(purchaseOrderId: string, createdById: string) {
  try {
    console.log(`[Notifications] Creating approval notifications for PO ${purchaseOrderId}, creator: ${createdById}`);

    // Get the purchase order details
    const purchaseOrder = await prisma.purchaseOrder.findUnique({
      where: { id: purchaseOrderId },
      include: {
        supplier: true,
        createdBy: true,
      },
    });

    if (!purchaseOrder) {
      console.error(`[Notifications] Purchase order ${purchaseOrderId} not found`);
      throw new Error('Purchase order not found');
    }

    console.log(`[Notifications] Found PO: ${purchaseOrder.id}, supplier: ${purchaseOrder.supplier.name}`);

    // Get users who can approve (excluding the creator)
    const approvalUsers = await getApprovalUsers();
    console.log(`[Notifications] Found ${approvalUsers.length} approval users:`, approvalUsers.map(u => `${u.name} (${u.role})`));

    const usersToNotify = approvalUsers.filter(user => user.id !== createdById);
    console.log(`[Notifications] Users to notify (excluding creator): ${usersToNotify.length}`);

    if (usersToNotify.length === 0) {
      console.log('[Notifications] No users to notify for PO approval');
      return [];
    }

    // Create notifications for each approval user
    const notifications: CreateNotificationData[] = usersToNotify.map(user => ({
      userId: user.id,
      title: 'Purchase Order Approval Required',
      message: `Purchase Order #${purchaseOrder.id.slice(-8).toUpperCase()} from ${purchaseOrder.supplier.name} (Rp ${Number(purchaseOrder.total).toLocaleString('id-ID')}) requires approval.`,
      type: 'PURCHASE_ORDER_APPROVAL' as const,
      purchaseOrderId: purchaseOrder.id,
      actionUrl: `/inventory/purchase-orders/${purchaseOrder.id}`,
      metadata: {
        supplierName: purchaseOrder.supplier.name,
        total: Number(purchaseOrder.total),
        createdBy: purchaseOrder.createdBy.name,
        orderDate: purchaseOrder.orderDate,
      },
    }));

    console.log(`[Notifications] Creating ${notifications.length} notifications:`, notifications.map(n => `${n.title} for ${n.userId}`));
    const results = await createBulkNotifications(notifications);
    console.log(`[Notifications] Successfully created ${results.length} approval notifications for PO ${purchaseOrderId}`);
    return results;
  } catch (error) {
    console.error('Error creating PO approval notifications:', error);
    throw error;
  }
}

/**
 * Create purchase order status change notifications
 */
export async function createPOStatusNotifications(
  purchaseOrderId: string,
  newStatus: string,
  actionUserId: string
) {
  try {
    // Get the purchase order details
    const purchaseOrder = await prisma.purchaseOrder.findUnique({
      where: { id: purchaseOrderId },
      include: {
        supplier: true,
        createdBy: true,
        approvedBy: true,
      },
    });

    if (!purchaseOrder) {
      throw new Error('Purchase order not found');
    }

    const notifications: CreateNotificationData[] = [];

    // Determine notification type and recipients based on status
    switch (newStatus) {
      case 'APPROVED':
        // Notify the creator that their PO was approved
        if (purchaseOrder.createdBy.id !== actionUserId) {
          notifications.push({
            userId: purchaseOrder.createdBy.id,
            title: 'Purchase Order Approved',
            message: `Your Purchase Order #${purchaseOrder.id.slice(-8).toUpperCase()} from ${purchaseOrder.supplier.name} has been approved.`,
            type: 'PURCHASE_ORDER_APPROVED',
            purchaseOrderId: purchaseOrder.id,
            actionUrl: `/inventory/purchase-orders/${purchaseOrder.id}`,
            metadata: {
              supplierName: purchaseOrder.supplier.name,
              total: Number(purchaseOrder.total),
              approvedBy: purchaseOrder.approvedBy?.name,
            },
          });
        }
        break;

      case 'REJECTED':
      case 'CANCELLED':
        // Notify the creator that their PO was rejected/cancelled
        if (purchaseOrder.createdBy.id !== actionUserId) {
          notifications.push({
            userId: purchaseOrder.createdBy.id,
            title: `Purchase Order ${newStatus === 'CANCELLED' ? 'Cancelled' : 'Rejected'}`,
            message: `Your Purchase Order #${purchaseOrder.id.slice(-8).toUpperCase()} from ${purchaseOrder.supplier.name} has been ${newStatus.toLowerCase()}.`,
            type: newStatus === 'CANCELLED' ? 'PO_CANCELLED' : 'PURCHASE_ORDER_REJECTED',
            purchaseOrderId: purchaseOrder.id,
            actionUrl: `/inventory/purchase-orders/${purchaseOrder.id}`,
            metadata: {
              supplierName: purchaseOrder.supplier.name,
              total: Number(purchaseOrder.total),
            },
          });
        }
        break;

      case 'ORDERED':
        // Notify creator that PO has been sent to supplier
        if (purchaseOrder.createdBy.id !== actionUserId) {
          notifications.push({
            userId: purchaseOrder.createdBy.id,
            title: 'Purchase Order Sent to Supplier',
            message: `Purchase Order #${purchaseOrder.id.slice(-8).toUpperCase()} has been sent to ${purchaseOrder.supplier.name}.`,
            type: 'PO_ORDERED',
            purchaseOrderId: purchaseOrder.id,
            actionUrl: `/inventory/purchase-orders/${purchaseOrder.id}`,
            metadata: {
              supplierName: purchaseOrder.supplier.name,
              total: Number(purchaseOrder.total),
            },
          });
        }
        break;

      case 'OVERDUE':
        // Notify all relevant staff about overdue order
        const overdueUsers = await prisma.user.findMany({
          where: {
            role: { in: ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'] },
            active: true,
          },
        });

        overdueUsers.forEach(user => {
          notifications.push({
            userId: user.id,
            title: 'Purchase Order Overdue',
            message: `Purchase Order #${purchaseOrder.id.slice(-8).toUpperCase()} from ${purchaseOrder.supplier.name} is overdue and requires attention.`,
            type: 'PO_OVERDUE',
            purchaseOrderId: purchaseOrder.id,
            actionUrl: `/inventory/purchase-orders/${purchaseOrder.id}`,
            metadata: {
              supplierName: purchaseOrder.supplier.name,
              total: Number(purchaseOrder.total),
            },
          });
        });
        break;

      case 'ON_HOLD':
        // Notify relevant staff about hold status
        const holdUsers = await prisma.user.findMany({
          where: {
            role: { in: ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'] },
            active: true,
            id: { not: actionUserId },
          },
        });

        holdUsers.forEach(user => {
          notifications.push({
            userId: user.id,
            title: 'Purchase Order On Hold',
            message: `Purchase Order #${purchaseOrder.id.slice(-8).toUpperCase()} from ${purchaseOrder.supplier.name} has been put on hold.`,
            type: 'PO_ON_HOLD',
            purchaseOrderId: purchaseOrder.id,
            actionUrl: `/inventory/purchase-orders/${purchaseOrder.id}`,
            metadata: {
              supplierName: purchaseOrder.supplier.name,
              total: Number(purchaseOrder.total),
            },
          });
        });
        break;

      case 'EXPEDITED':
        // Notify warehouse staff about expedited order
        const expediteUsers = await prisma.user.findMany({
          where: {
            role: { in: ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'WAREHOUSE_STAFF'] },
            active: true,
            id: { not: actionUserId },
          },
        });

        expediteUsers.forEach(user => {
          notifications.push({
            userId: user.id,
            title: 'Purchase Order Expedited',
            message: `Purchase Order #${purchaseOrder.id.slice(-8).toUpperCase()} from ${purchaseOrder.supplier.name} has been marked as expedited - high priority.`,
            type: 'PO_EXPEDITED',
            purchaseOrderId: purchaseOrder.id,
            actionUrl: `/inventory/purchase-orders/${purchaseOrder.id}`,
            metadata: {
              supplierName: purchaseOrder.supplier.name,
              total: Number(purchaseOrder.total),
            },
          });
        });
        break;

      case 'RECEIVED':
      case 'PARTIALLY_RECEIVED':
        // Notify relevant users about receiving status
        const usersToNotify = [purchaseOrder.createdBy.id];
        if (purchaseOrder.approvedBy && purchaseOrder.approvedBy.id !== purchaseOrder.createdBy.id) {
          usersToNotify.push(purchaseOrder.approvedBy.id);
        }

        usersToNotify.forEach(userId => {
          if (userId !== actionUserId) {
            notifications.push({
              userId,
              title: `Purchase Order ${newStatus === 'RECEIVED' ? 'Received' : 'Partially Received'}`,
              message: `Purchase Order #${purchaseOrder.id.slice(-8).toUpperCase()} from ${purchaseOrder.supplier.name} has been ${newStatus === 'RECEIVED' ? 'fully received' : 'partially received'}.`,
              type: 'PURCHASE_ORDER_RECEIVED',
              purchaseOrderId: purchaseOrder.id,
              actionUrl: `/inventory/purchase-orders/${purchaseOrder.id}`,
              metadata: {
                supplierName: purchaseOrder.supplier.name,
                total: Number(purchaseOrder.total),
                status: newStatus,
              },
            });
          }
        });
        break;
    }

    if (notifications.length > 0) {
      const results = await createBulkNotifications(notifications);
      console.log(`Created ${results.length} status notifications for PO ${purchaseOrderId} (${newStatus})`);
      return results;
    }

    return [];
  } catch (error) {
    console.error('Error creating PO status notifications:', error);
    throw error;
  }
}

/**
 * Mark notifications as read for a purchase order when user views it
 */
export async function markPONotificationsAsRead(purchaseOrderId: string, userId: string) {
  try {
    const result = await prisma.notification.updateMany({
      where: {
        purchaseOrderId,
        userId,
        isRead: false,
      },
      data: {
        isRead: true,
      },
    });

    console.log(`Marked ${result.count} PO notifications as read for user ${userId}`);
    return result;
  } catch (error) {
    console.error('Error marking PO notifications as read:', error);
    throw error;
  }
}
