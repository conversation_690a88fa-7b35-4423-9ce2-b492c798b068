import { NextRequest, NextResponse } from 'next/server';
import { verifyAuthToken } from '@/auth';
import { prisma } from '@/auth';

// GET /api/purchase-orders/analytics - Get PO analytics and status breakdown
export async function GET(request: NextRequest) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const url = new URL(request.url);
    const dateFrom = url.searchParams.get('dateFrom');
    const dateTo = url.searchParams.get('dateTo');
    const supplierId = url.searchParams.get('supplierId');

    // Build date filter
    const dateFilter: any = {};
    if (dateFrom) {
      dateFilter.gte = new Date(dateFrom);
    }
    if (dateTo) {
      dateFilter.lte = new Date(dateTo);
    }

    // Build where clause
    const where: any = {};
    if (Object.keys(dateFilter).length > 0) {
      where.createdAt = dateFilter;
    }
    if (supplierId) {
      where.supplierId = supplierId;
    }

    // Get status breakdown
    const statusBreakdown = await prisma.purchaseOrder.groupBy({
      by: ['status'],
      where,
      _count: {
        id: true,
      },
      _sum: {
        total: true,
      },
    });

    // Get priority breakdown
    const priorityBreakdown = await prisma.purchaseOrder.groupBy({
      by: ['priority'],
      where,
      _count: {
        id: true,
      },
      _sum: {
        total: true,
      },
    });

    // Get overdue POs
    const overduePOs = await prisma.purchaseOrder.findMany({
      where: {
        ...where,
        expectedDeliveryDate: {
          lt: new Date(),
        },
        status: {
          in: ['ORDERED', 'EXPEDITED', 'PARTIALLY_RECEIVED'],
        },
      },
      include: {
        supplier: true,
      },
      orderBy: {
        expectedDeliveryDate: 'asc',
      },
    });

    // Get recent status changes
    const recentStatusChanges = await prisma.pOStatusHistory.findMany({
      where: {
        purchaseOrder: where,
      },
      include: {
        purchaseOrder: {
          include: {
            supplier: true,
          },
        },
        changedBy: {
          select: { id: true, name: true, email: true },
        },
      },
      orderBy: {
        changedAt: 'desc',
      },
      take: 20,
    });

    // Get supplier performance
    const supplierPerformance = await prisma.purchaseOrder.groupBy({
      by: ['supplierId'],
      where: {
        ...where,
        status: 'RECEIVED',
      },
      _count: {
        id: true,
      },
      _avg: {
        total: true,
      },
    });

    // Get supplier details for performance data
    const supplierIds = supplierPerformance.map(sp => sp.supplierId);
    const suppliers = await prisma.supplier.findMany({
      where: {
        id: {
          in: supplierIds,
        },
      },
      select: {
        id: true,
        name: true,
      },
    });

    // Combine supplier performance with names
    const supplierPerformanceWithNames = supplierPerformance.map(sp => {
      const supplier = suppliers.find(s => s.id === sp.supplierId);
      return {
        ...sp,
        supplierName: supplier?.name || 'Unknown',
      };
    });

    // Calculate delivery performance
    const deliveryPerformance = await prisma.purchaseOrder.findMany({
      where: {
        ...where,
        status: 'RECEIVED',
        expectedDeliveryDate: {
          not: null,
        },
        actualDeliveryDate: {
          not: null,
        },
      },
      select: {
        expectedDeliveryDate: true,
        actualDeliveryDate: true,
        supplierId: true,
      },
    });

    // Calculate on-time delivery metrics
    const onTimeDeliveries = deliveryPerformance.filter(po => {
      if (!po.expectedDeliveryDate || !po.actualDeliveryDate) return false;
      return po.actualDeliveryDate <= po.expectedDeliveryDate;
    });

    const onTimeDeliveryRate = deliveryPerformance.length > 0 
      ? (onTimeDeliveries.length / deliveryPerformance.length) * 100 
      : 0;

    // Get total counts
    const totalPOs = await prisma.purchaseOrder.count({ where });
    const totalValue = await prisma.purchaseOrder.aggregate({
      where,
      _sum: {
        total: true,
      },
    });

    return NextResponse.json({
      summary: {
        totalPOs,
        totalValue: totalValue._sum.total || 0,
        overdueCount: overduePOs.length,
        onTimeDeliveryRate: Math.round(onTimeDeliveryRate * 100) / 100,
      },
      statusBreakdown: statusBreakdown.map(sb => ({
        status: sb.status,
        count: sb._count.id,
        totalValue: sb._sum.total || 0,
      })),
      priorityBreakdown: priorityBreakdown.map(pb => ({
        priority: pb.priority,
        count: pb._count.id,
        totalValue: pb._sum.total || 0,
      })),
      overduePOs: overduePOs.map(po => ({
        id: po.id,
        supplier: po.supplier.name,
        total: po.total,
        expectedDeliveryDate: po.expectedDeliveryDate,
        daysOverdue: po.expectedDeliveryDate 
          ? Math.ceil((new Date().getTime() - po.expectedDeliveryDate.getTime()) / (1000 * 60 * 60 * 24))
          : 0,
        status: po.status,
        priority: po.priority,
      })),
      recentStatusChanges: recentStatusChanges.map(change => ({
        id: change.id,
        purchaseOrderId: change.purchaseOrderId,
        supplier: change.purchaseOrder.supplier.name,
        fromStatus: change.fromStatus,
        toStatus: change.toStatus,
        reason: change.reason,
        notes: change.notes,
        changedBy: change.changedBy.name,
        changedAt: change.changedAt,
      })),
      supplierPerformance: supplierPerformanceWithNames.map(sp => ({
        supplierId: sp.supplierId,
        supplierName: sp.supplierName,
        completedOrders: sp._count.id,
        averageOrderValue: sp._avg.total || 0,
      })),
    });
  } catch (error) {
    console.error('Error fetching PO analytics:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
